import { Link } from "react-router-dom";
import {
  ArrowRight,
  DollarSign,
  TrendingUp,
  Shield,
  Smartphone,
} from "lucide-react";
import { Button } from "../components/ui/button";

const LandingPage = () => {
  const features = [
    {
      icon: TrendingUp,
      title: "Track Expenses",
      description:
        "Monitor your spending patterns and identify areas for improvement.",
    },
    {
      icon: DollarSign,
      title: "Budget Management",
      description: "Set budgets and get alerts when you're approaching limits.",
    },
    {
      icon: Shield,
      title: "Secure & Private",
      description: "Your financial data is encrypted and protected.",
    },
    {
      icon: Smartphone,
      title: "Mobile Friendly",
      description: "Access your finances anywhere, anytime.",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Header */}
      <header className=" shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-8 w-8 text-green-600" />
              <h1 className="text-2xl font-bold text-gray-900">
                Finance Tracker
              </h1>
            </div>
            <div className="space-x-4">
              <Link to={"auth/login"}>
                <Button variant="default">Login</Button>
              </Link>
              <Link to={"auth/register"}>
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            Take Control of Your
            <span className="text-green-600"> Finances</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Track expenses, manage budgets, and achieve your financial goals
            with our intuitive and powerful finance tracking platform.
          </p>
          <div className="space-x-4">
            <Link to={"auth/register"}>
              <Button size="lg" className="text-lg px-8 py-3">
                Start Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link to={"auth/login"}>
              <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Everything you need to manage your money
            </h2>
            <p className="text-lg text-gray-600">
              Powerful features to help you stay on top of your finances
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center p-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                  <feature.icon className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-green-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to get started?
          </h2>
          <p className="text-xl text-green-100 mb-8">
            Join thousands of users who are already managing their finances
            better.
          </p>
          <Link to={"auth/register"}>
            <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
              Create Your Account
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;
